import express from "express";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import dotenv from "dotenv";
import bodyParser from "body-parser";
import cookieParser from "cookie-parser";
import checkUser from "./middlewares/checkUser.js";
import AgentLoader from "./utils/agentLoader.js";

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.APP_PORT || 5173;

// Initialize agent loader
const agentLoader = new AgentLoader();

// Middleware
app.use(bodyParser.json());
app.use(cookieParser());
app.use(express.static(join(__dirname, "dist")));

// API Routes

// Get available agents
app.get("/api/agents", (_req, res) => {
  try {
    const agents = agentLoader.getAgents();
    res.json(agents);
  } catch (error) {
    console.error("Error getting agents:", error);
    res.status(500).json({
      ok: false,
      message: "Failed to load agents",
    });
  }
});

// Get current user info
app.get("/api/@me", async (req, res) => {
  const { hf_token } = req.cookies;

  if (!hf_token) {
    return res.status(401).json({
      ok: false,
      message: "Unauthorized",
    });
  }

  try {
    // Verify token with Hugging Face API
    const response = await fetch("https://huggingface.co/api/whoami", {
      headers: {
        Authorization: `Bearer ${hf_token}`,
      },
    });

    if (response.ok) {
      const userData = await response.json();
      res.json({
        ok: true,
        user: userData,
      });
    } else {
      res.status(401).json({
        ok: false,
        message: "Invalid token",
      });
    }
  } catch (error) {
    console.error("Error verifying user:", error);
    res.status(500).json({
      ok: false,
      message: "Internal server error",
    });
  }
});

// AI Chat endpoint
app.post("/api/ask-ai", async (req, res) => {
  const { prompt, html, previousPrompt, agentMode, agentName } = req.body;

  console.log("🤖 AI Request received:", {
    prompt: prompt.substring(0, 50) + "...",
    hasHtml: !!html,
    agentMode: !!agentMode,
    agentName: agentName || "none",
  });

  if (!prompt) {
    return res.status(400).json({
      ok: false,
      message: "Prompt is required",
    });
  }

  try {
    // Prepare the base system message for HTML editing
    const baseSystemMessage = `You are an expert web developer assistant. Your task is to help users create and modify HTML code based on their requests.

Current HTML context:
${html || "No HTML provided - starting fresh"}

${previousPrompt ? `Previous request: ${previousPrompt}` : ""}

Instructions:
- Provide complete, valid HTML code
- Include proper DOCTYPE, html, head, and body tags
- Add appropriate meta tags and viewport settings
- Use modern CSS and JavaScript when needed
- Make the code responsive and accessible
- Only return the HTML code, no explanations unless specifically asked

User request: ${prompt}`;

    // Use agent-specific system message if agent mode is enabled
    let systemMessage = baseSystemMessage;
    if (agentMode && agentName) {
      const agentSystemMessage = agentLoader.createAgentSystemPrompt(
        agentName,
        baseSystemMessage
      );
      if (agentSystemMessage !== baseSystemMessage) {
        systemMessage = agentSystemMessage;
        console.log(`🤖 Using agent: ${agentName}`);
      } else {
        console.warn(
          `⚠️ Agent not found: ${agentName}, using default system message`
        );
      }
    }

    // Set headers for streaming response
    res.setHeader("Content-Type", "text/plain");
    res.setHeader("Transfer-Encoding", "chunked");

    // First try streaming, if that fails, fall back to non-streaming
    try {
      const apiResponse = await fetch(
        process.env.API_BASE_URL + "/chat/completions",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${process.env.API_KEY}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model: process.env.API_MODEL,
            messages: [
              {
                role: "system",
                content: systemMessage,
              },
            ],
            stream: true,
            max_tokens: 4000,
            temperature: 0.7,
          }),
        }
      );

      if (
        apiResponse.ok &&
        apiResponse.headers.get("content-type")?.includes("text/event-stream")
      ) {
        // Handle streaming response
        const reader = apiResponse.body.getReader();
        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split("\n");

            for (const line of lines) {
              if (line.startsWith("data: ")) {
                const data = line.slice(6);
                if (data === "[DONE]") {
                  res.end();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    res.write(content);
                  }
                } catch (_parseError) {
                  // Skip invalid JSON lines
                  continue;
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }
        res.end();
        return;
      }
    } catch (streamError) {
      console.log(
        "Streaming failed, falling back to non-streaming:",
        streamError.message
      );
    }

    // Fallback to non-streaming
    const apiResponse = await fetch(
      process.env.API_BASE_URL + "/chat/completions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: process.env.API_MODEL,
          messages: [
            {
              role: "system",
              content: systemMessage,
            },
          ],
          stream: false,
          max_tokens: 4000,
          temperature: 0.7,
        }),
      }
    );

    if (!apiResponse.ok) {
      const errorText = await apiResponse.text();
      throw new Error(
        `API request failed: ${apiResponse.status} - ${errorText}`
      );
    }

    const data = await apiResponse.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error("No content received from AI");
    }

    // Simulate streaming for better UX
    const words = content.split(" ");
    for (let i = 0; i < words.length; i++) {
      const word = words[i] + (i < words.length - 1 ? " " : "");
      res.write(word);
      // Small delay to simulate streaming
      await new Promise((resolve) => setTimeout(resolve, 20));
    }

    res.end();
  } catch (error) {
    console.error("Error calling AI API:", error);
    if (!res.headersSent) {
      res.status(500).json({
        ok: false,
        message: error.message || "Failed to process AI request",
      });
    }
  }
});

// Deploy to Hugging Face Spaces
app.post("/api/deploy", checkUser, async (req, res) => {
  const { title, path, html } = req.body;
  const { hf_token } = req.cookies;

  if (!title || !html) {
    return res.status(400).json({
      ok: false,
      message: "Title and HTML are required",
    });
  }

  try {
    // Create or update Hugging Face Space
    const spaceId = path || `${title.toLowerCase().replace(/[^a-z0-9]/g, "-")}`;

    // This is a simplified implementation - you may need to use the Hugging Face Hub library
    // for more complex operations
    const response = await fetch(`https://huggingface.co/api/repos/create`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${hf_token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "space",
        name: spaceId,
        private: false,
        sdk: "static",
      }),
    });

    if (response.ok || response.status === 409) {
      // 409 means space already exists
      // Upload the HTML file
      const _uploadResponse = await fetch(
        `https://huggingface.co/api/upload/${spaceId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${hf_token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            files: [
              {
                path: "index.html",
                content: html,
              },
            ],
            commit_message: `Update HTML content`,
          }),
        }
      );

      res.json({
        ok: true,
        path: spaceId,
        message: "Successfully deployed to Hugging Face Spaces",
      });
    } else {
      throw new Error(`Failed to create space: ${response.status}`);
    }
  } catch (error) {
    console.error("Error deploying to HF:", error);
    res.status(500).json({
      ok: false,
      message: error.message || "Failed to deploy",
    });
  }
});

// Serve React app for all other routes
app.get("*", (_req, res) => {
  res.sendFile(join(__dirname, "dist", "index.html"));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📝 API Provider: ${process.env.API_PROVIDER}`);
  console.log(`🤖 AI Model: ${process.env.API_MODEL}`);
  console.log(`🔗 API Base URL: ${process.env.API_BASE_URL}`);
});
